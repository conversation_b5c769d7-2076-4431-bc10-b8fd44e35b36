import React, { useState, useEffect, useRef } from 'react';
import { X, Mic, PhoneOff } from 'lucide-react';

const WebCallWidget = ({
    customerId,
    callHistoryId,
    onClose,
    onConnect,
    onTranscriptUpdate,
    customPrompt,
    customFirstMessage,
    webhookData,
    conversationLog,
}) => {
    const [isRecording, setIsRecording] = useState(false);
    const [audioQueue, setAudioQueue] = useState([]);
    const [isPlaying, setIsPlaying] = useState(false);
    const [localTranscript, setLocalTranscript] = useState([]);
    const [audioBuffer, setAudioBuffer] = useState([]);
    const utteranceEndTimerRef = useRef(null);
    const isMountedRef = useRef(true);

    const audioRef = useRef(null);
    const wsRef = useRef(null);
    const audioContextRef = useRef(null);
    const audioNodeRef = useRef(null);
    const streamRef = useRef(null);

    useEffect(() => {
        if (conversationLog && conversationLog.length > 0) {
            const lastMessage = conversationLog[conversationLog.length - 1];
            if (lastMessage.source === 'user' || lastMessage.source === 'human') {
                console.log('Last message was from user, clearing audio buffer');
                // Clear the audio buffer and queue, and stop playback
                setAudioBuffer([]);
                setAudioQueue([]);
                if (audioRef.current) {
                    // Safely stop audio playback
                    try {
                        audioRef.current.pause();
                        audioRef.current.currentTime = 0;
                        audioRef.current.src = '';
                    } catch (e) {
                        console.warn('Error stopping audio:', e);
                    }
                }
                setIsPlaying(false);
                clearTimeout(utteranceEndTimerRef.current);
            }
        }
    }, [conversationLog]);

    // This function is called after a pause in receiving audio
    const playBufferedAudio = () => {
        setAudioBuffer(currentBuffer => {
            if (currentBuffer.length === 0) return [];

            // Concatenate all buffered chunks into a single audio buffer
            const totalLength = currentBuffer.reduce((acc, chunk) => acc + chunk.length, 0);
            const concatenated = new Uint8Array(totalLength);
            let offset = 0;
            for (const chunk of currentBuffer) {
                concatenated.set(chunk, offset);
                offset += chunk.length;
            }

            // Create a single WAV file from the complete utterance and add it to the play queue
            const wavHeader = new Uint8Array(createWavHeader(concatenated.length, 24000, 1, 16));
            const wavBlob = new Blob([wavHeader, concatenated], { type: 'audio/wav' });
            setAudioQueue(prev => [...prev, wavBlob]);

            return []; // Clear the buffer for the next utterance
        });
    };

    const createWavHeader = (dataLength, sampleRate, numChannels, bitsPerSample) => {
        const buffer = new ArrayBuffer(44);
        const view = new DataView(buffer);

        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };

        const blockAlign = numChannels * (bitsPerSample / 8);
        const byteRate = sampleRate * blockAlign;

        writeString(0, 'RIFF');
        view.setUint32(4, 36 + dataLength, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true); // PCM
        view.setUint16(22, numChannels, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, byteRate, true);
        view.setUint16(32, blockAlign, true);
        view.setUint16(34, bitsPerSample, true);
        writeString(36, 'data');
        view.setUint32(40, dataLength, true);

        return buffer;
    };

    const handleServerMessage = (event) => {
        try {
            const message = JSON.parse(event.data);
            onTranscriptUpdate(message);

            if (message.type === 'audio' && message.audio) {
                // When an audio chunk arrives, clear the timer and add the chunk to the buffer
                clearTimeout(utteranceEndTimerRef.current);
                const pcmData = Uint8Array.from(atob(message.audio), c => c.charCodeAt(0));
                setAudioBuffer(prev => [...prev, pcmData]);

                // Set a timer to process the buffer after a short silence
                utteranceEndTimerRef.current = setTimeout(playBufferedAudio, 300);
            } else if (message.type === 'transcript' || message.type === 'human') {
                setLocalTranscript(prev => [...prev, { sender: message.sender, text: message.transcript }]);
            }
        } catch (error) {
            console.error("Error handling server message:", error);
        }
    };

    const startCall = async () => {
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const apiUrl = new URL(import.meta.env.VITE_API_URL || 'http://localhost:8000');
        const wsHost = apiUrl.host;
        const wsUrl = `${wsProtocol}//${wsHost}/api/voice/web-call`;

        setAudioQueue([]);
        wsRef.current = new WebSocket(wsUrl);

        wsRef.current.onopen = async () => {
            onConnect();

            const initMessage = {
                type: 'init',
                customerId,
                callHistoryId,
                customPrompt,
                customFirstMessage,
                webhookData: webhookData ? JSON.parse(webhookData) : {},
            };
            wsRef.current.send(JSON.stringify(initMessage));

            setIsRecording(true);
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: { sampleRate: 16000, channelCount: 1 } });
                streamRef.current = stream;

                audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)({
                    sampleRate: 16000,
                });

                await audioContextRef.current.audioWorklet.addModule('/recorderProcessor.js');

                const source = audioContextRef.current.createMediaStreamSource(stream);
                audioNodeRef.current = new AudioWorkletNode(audioContextRef.current, 'recorder-processor');

                audioNodeRef.current.port.onmessage = (event) => {
                    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
                        wsRef.current.send(event.data);
                    }
                };

                source.connect(audioNodeRef.current);
                audioNodeRef.current.connect(audioContextRef.current.destination);

            } catch (err) {
                console.error("Error getting user media or setting up audio processor:", err);
                onTranscriptUpdate({ type: 'transcript', sender: 'system', transcript: 'Could not start microphone.' });
            }
        };

        wsRef.current.onmessage = handleServerMessage;

        wsRef.current.onerror = (error) => {
            console.error("WebSocket Error:", error);
            onTranscriptUpdate({ type: 'transcript', sender: 'system', transcript: 'Connection error.' });
            setIsRecording(false);
        };

        wsRef.current.onclose = (event) => {
            setIsRecording(false);
            if (streamRef.current) {
                streamRef.current.getTracks().forEach(track => track.stop());
            }
            if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
                audioContextRef.current.close();
            }
            onClose();
        };
    };

    const endCall = () => {
        if (wsRef.current) {
            wsRef.current.close();
        }
    };

    useEffect(() => {
        if (audioQueue.length > 0 && !isPlaying && audioRef.current) {
            const nextAudioBlob = audioQueue[0];
            const url = URL.createObjectURL(nextAudioBlob);

            // Set up the audio element
            audioRef.current.src = url;

            // Set up event handlers before playing
            const handleEnded = () => {
                URL.revokeObjectURL(url);
                if (isMountedRef.current) {
                    setIsPlaying(false);
                    setAudioQueue(prev => prev.slice(1));
                }
                // Clean up event listener
                if (audioRef.current) {
                    audioRef.current.removeEventListener('ended', handleEnded);
                }
            };

            const handleError = (e) => {
                console.error("Audio error:", e);
                URL.revokeObjectURL(url);
                if (isMountedRef.current) {
                    setIsPlaying(false);
                    setAudioQueue(prev => prev.slice(1));
                }
                // Clean up event listeners
                if (audioRef.current) {
                    audioRef.current.removeEventListener('ended', handleEnded);
                    audioRef.current.removeEventListener('error', handleError);
                }
            };

            audioRef.current.addEventListener('ended', handleEnded);
            audioRef.current.addEventListener('error', handleError);

            // Attempt to play with proper error handling
            const playPromise = audioRef.current.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    setIsPlaying(true);
                }).catch(e => {
                    console.error("Audio play failed:", e);
                    // Clean up on play failure
                    URL.revokeObjectURL(url);
                    setIsPlaying(false);
                    setAudioQueue(prev => prev.slice(1));
                    if (audioRef.current) {
                        audioRef.current.removeEventListener('ended', handleEnded);
                        audioRef.current.removeEventListener('error', handleError);
                    }
                });
            }
        }
    }, [audioQueue, isPlaying]);

    useEffect(() => {
        startCall();
        return () => {
            // Mark component as unmounted
            isMountedRef.current = false;

            // Cleanup function
            if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
                wsRef.current.close();
            }
            if (audioRef.current) {
                try {
                    audioRef.current.pause();
                    audioRef.current.src = '';
                } catch (e) {
                    console.warn('Error cleaning up audio:', e);
                }
            }
            if (streamRef.current) {
                streamRef.current.getTracks().forEach(track => track.stop());
            }
            if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
                audioContextRef.current.close();
            }
            clearTimeout(utteranceEndTimerRef.current);
            // Clean up any remaining audio URLs
            audioQueue.forEach(blob => {
                if (blob instanceof Blob) {
                    URL.revokeObjectURL(URL.createObjectURL(blob));
                }
            });
        };
    }, []);

    // This component is now headless and doesn't render anything itself.
    // The audio element is necessary for playing back the agent's voice.
    return <audio ref={audioRef} className="hidden" />;
};

export default WebCallWidget;

