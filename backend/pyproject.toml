[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "backend"
version = "1.0.0"
requires-python = ">=3.12"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
]
dependencies = [
    "fastapi",
    "uvicorn[standard]==0.29.0",
    "starlette==0.37.2",
    "python-dotenv",
    "sqlalchemy",
    "alembic",
    "psycopg2-binary",
    "pydantic-settings",
    "asyncio",
    "websockets>=12.0",
    "elevenlabs",
    "litellm",
    "loguru",
    "twilio",
    "hume",
    "pydantic[email]>=2.11.7",
    "assemblyai",
    "google-generativeai",
    "webrtcvad",
    "setuptools",
    "aiofiles",
    "python-multipart>=0.0.20",
    "deepgram-sdk>=4.7.0",
    "g711",
    "resampy",
    "httpx-ws>=0.7.2",
    "vosk",
    "langchain>=0.3.27",
    "langchain-google-genai",
    "langchain-core",
    "openai",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.24.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.24.0",
    "pytest-cov>=4.0.0",
    "vosk",
]


[project.scripts]
ecommerce-server = "app.main:main"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "twilio.*",
    "elevenlabs.*",
    "redis.*",
    "celery.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
]
filterwarnings = [
    "ignore:pkg_resources is deprecated as an API:UserWarning",
    "ignore:Deprecated call to `pkg_resources.declare_namespace('google')`:DeprecationWarning",
]


[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
]
