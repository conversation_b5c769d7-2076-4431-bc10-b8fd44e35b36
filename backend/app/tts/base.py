from abc import ABC, abstractmethod
from typing import AsyncGenerator


class TTSBase(ABC):
    """Abstract base class for text-to-speech services."""

    @abstractmethod
    async def stream(
        self, text_generator: AsyncGenerator[str, None], interruption_event
    ) -> AsyncGenerator[str, None]:
        """
        Streams audio from a text generator.

        Args:
            text_generator: An async generator that yields text chunks.
            interruption_event: An event to signal interruption.
        """
        pass
