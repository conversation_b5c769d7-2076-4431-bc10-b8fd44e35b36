import asyncio
import base64
import json
import logging
import websockets
from typing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from elevenlabs.client import Eleven<PERSON>abs as Eleven<PERSON>absClient

from ..config import settings
from .base import TTSBase
from ..performance_tracker import track_latency

logger = logging.getLogger(__name__)


class ElevenLabsTTS(TTSBase):
    def __init__(self):
        self.client = ElevenLabsClient(api_key=settings.elevenlabs_api_key)
        if settings.elevenlabs_api_key:
            logger.info("ElevenLabsService initialized with API key.")
        else:
            logger.warning("ElevenLabsService is missing API key in settings.")

    @track_latency("tts", "elevenlabs_stream")
    async def stream(
        self,
        text_generator: AsyncGenerator[str, None],
        interruption_event: asyncio.Event,
    ) -> AsyncGenerator[str, None]:
        """
        Generates audio from a text generator and yields base64 encoded audio chunks.
        This method is now transport-agnostic.
        """
        voice_id = settings.elevenlabs_voice_id
        model_id = "eleven_turbo_v2_5"

        # Collect the full text from the generator to send to ElevenLabs
        text = "".join([chunk async for chunk in text_generator])
        if not text:
            return

        try:
            stream_kwargs = {
                "text": text,
                "voice_id": voice_id,
                "model_id": model_id,
                "output_format": "pcm_24000",
            }
            if settings.elevenlabs_language_code:
                stream_kwargs["language_code"] = settings.elevenlabs_language_code

            audio_stream = self.client.text_to_speech.stream(**stream_kwargs)

            for audio_chunk in audio_stream:
                if interruption_event.is_set():
                    logger.info("Interruption detected, stopping ElevenLabs TTS.")
                    break
                yield base64.b64encode(audio_chunk).decode("utf-8")

        except Exception as e:
            logger.error(f"Error in ElevenLabs stream: {e}")

    @track_latency("tts", "elevenlabs_stream_realtime")
    async def stream_realtime(
        self, text_generator: AsyncGenerator[str, None], interruption_event: asyncio.Event
    ) -> AsyncGenerator[str, None]:
        """
        Realtime TTS streaming using ElevenLabs WebSocket API.
        Streams audio as text chunks become available.
        """
        logger.info("Starting ElevenLabs realtime TTS streaming...")

        if not settings.elevenlabs_api_key:
            logger.error("ElevenLabs API key not configured")
            return

        if not settings.elevenlabs_voice_id:
            logger.error("ElevenLabs voice ID not configured")
            return

        try:
            # WebSocket URI for ElevenLabs TTS streaming
            voice_id = settings.elevenlabs_voice_id
            model_id = "eleven_flash_v2_5"  # Use flash model for lower latency
            output_format = "pcm_24000"  # PCM format at 24kHz for compatibility
            uri = f"wss://api.elevenlabs.io/v1/text-to-speech/{voice_id}/stream-input?model_id={model_id}&output_format={output_format}"

            # Set up headers with API key
            headers = {"xi-api-key": settings.elevenlabs_api_key}

            async with websockets.connect(uri, extra_headers=headers) as websocket:
                # Initialize connection with voice settings
                init_message = {
                    "text": " ",  # Initial space to keep connection alive
                    "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "use_speaker_boost": False},
                    "generation_config": {"chunk_length_schedule": [50, 120, 160, 250]},  # Optimized for low latency
                }

                if settings.elevenlabs_language_code:
                    init_message["language_code"] = settings.elevenlabs_language_code

                await websocket.send(json.dumps(init_message))
                logger.debug("Sent initialization message to ElevenLabs")

                # Create tasks for sending text and receiving audio
                async def send_text():
                    try:
                        async for text_chunk in text_generator:
                            if interruption_event.is_set():
                                logger.info("Interruption detected, stopping text sending.")
                                break

                            if text_chunk.strip():
                                logger.debug(f"Sending text chunk to ElevenLabs: {text_chunk[:50]}...")
                                await websocket.send(json.dumps({"text": text_chunk}))

                        # Send empty string to indicate end of text
                        await websocket.send(json.dumps({"text": ""}))
                        logger.debug("Sent end-of-text signal to ElevenLabs")

                    except Exception as e:
                        logger.error(f"Error sending text to ElevenLabs: {e}")

                async def receive_audio():
                    try:
                        while True:
                            if interruption_event.is_set():
                                logger.info("Interruption detected, stopping audio reception.")
                                break

                            try:
                                message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                                data = json.loads(message)

                                logger.debug(f"Received WebSocket message: {list(data.keys())}")

                                if data.get("audio"):
                                    # ElevenLabs WebSocket API already returns base64-encoded audio
                                    audio_chunk = data["audio"]
                                    logger.debug(f"Received audio chunk of length: {len(audio_chunk)}")
                                    yield audio_chunk

                                elif data.get("isFinal"):
                                    logger.debug("Received final message from ElevenLabs")
                                    break

                                elif data.get("error"):
                                    logger.error(f"ElevenLabs WebSocket error: {data['error']}")
                                    break

                            except asyncio.TimeoutError:
                                # Continue listening for more audio
                                continue
                            except websockets.exceptions.ConnectionClosed:
                                logger.info("ElevenLabs WebSocket connection closed")
                                break

                    except Exception as e:
                        logger.error(f"Error receiving audio from ElevenLabs: {e}")

                # Start both tasks concurrently
                send_task = asyncio.create_task(send_text())

                # Yield audio chunks as they arrive
                async for audio_chunk in receive_audio():
                    yield audio_chunk

                # Wait for send task to complete
                await send_task

        except Exception as e:
            logger.error(f"Error in ElevenLabs realtime TTS stream: {e}", exc_info=True)
            raise
