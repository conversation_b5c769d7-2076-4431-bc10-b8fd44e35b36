import asyncio
import base64
import logging
from typing import As<PERSON><PERSON>enerator

from elevenlabs.client import <PERSON><PERSON><PERSON><PERSON> as ElevenLabsClient

from ..config import settings
from .base import TTSBase

logger = logging.getLogger(__name__)


class ElevenLabsTTS(TTSBase):
    def __init__(self):
        self.client = ElevenLabsClient(api_key=settings.elevenlabs_api_key)
        if settings.elevenlabs_api_key:
            logger.info("ElevenLabsService initialized with API key.")
        else:
            logger.warning("ElevenLabsService is missing API key in settings.")

    async def stream(
        self,
        text_generator: AsyncGenerator[str, None],
        interruption_event: asyncio.Event,
    ) -> AsyncGenerator[str, None]:
        """
        Generates audio from a text generator and yields base64 encoded audio chunks.
        This method is now transport-agnostic.
        """
        voice_id = settings.elevenlabs_voice_id
        model_id = "eleven_turbo_v2_5"

        # Collect the full text from the generator to send to ElevenLabs
        text = "".join([chunk async for chunk in text_generator])
        if not text:
            return

        try:
            stream_kwargs = {
                "text": text,
                "voice_id": voice_id,
                "model_id": model_id,
                "output_format": "pcm_24000",
            }
            if settings.elevenlabs_language_code:
                stream_kwargs["language_code"] = settings.elevenlabs_language_code

            audio_stream = self.client.text_to_speech.stream(**stream_kwargs)

            for audio_chunk in audio_stream:
                if interruption_event.is_set():
                    logger.info("Interruption detected, stopping ElevenLabs TTS.")
                    break
                yield base64.b64encode(audio_chunk).decode("utf-8")

        except Exception as e:
            logger.error(f"Error in ElevenLabs stream: {e}")

    def create_voice_call_context(self, customer_data, cart_data):
        """
        Creates a context for the voice call based on customer and cart data.
        This is a placeholder and should be expanded with more sophisticated context generation.
        """
        return {"customer": customer_data, "cart": cart_data}

    def get_signed_url(self, customer_data):
        """
        This is a placeholder for getting a signed URL for ElevenLabs.
        The actual implementation would depend on the specific ElevenLabs product being used.
        For their new Voice Calls API, this might involve a different setup.
        """
        base_url = "wss://api.elevenlabs.io/v1/voice-calls"
        signed_url = f"{base_url}?api_key={settings.elevenlabs_api_key}"

        if customer_data:
            signed_url += f"&customer_id={customer_data.get('id')}"

        return {"success": True, "signed_url": signed_url}
