import logging
import base64
import async<PERSON>
from ..config import settings
from .base import TTS<PERSON><PERSON>
from typing import AsyncGenerator
from deepgram import Deepgram<PERSON>lient, SpeakOptions

logger = logging.getLogger(__name__)


class NovaTTS(TTSBase):
    def __init__(self):
        self.client = DeepgramClient(settings.nova_api_key)
        logger.info("NovaTTS (Deepgram) service initialized.")

    async def stream(
        self, text_generator: AsyncGenerator[str, None], interruption_event: asyncio.Event
    ) -> AsyncGenerator[str, None]:
        logger.info("Starting Nova (Deepgram) TTS generation...")
        text = "".join([chunk async for chunk in text_generator])
        if not text:
            return

        try:
            options = SpeakOptions(
                model=settings.nova_voice_id,
                encoding="linear16",
                sample_rate=24000,
            )

            source = {"text": text}

            response = self.client.speak.v("1").stream(source, options)

            audio_stream = response.stream

            chunk_size = 1024
            while True:
                if interruption_event.is_set():
                    logger.info("Interruption detected, stopping TTS.")
                    break

                chunk = audio_stream.read(chunk_size)
                if not chunk:
                    break

                yield base64.b64encode(chunk).decode("utf-8")

        except Exception as e:
            logger.error(f"Error in Nova (Deepgram) TTS stream: {e}", exc_info=True)
            raise

    async def list_voices(self):
        logger.info("Listing Nova (Deepgram) voices is not supported via the SDK.")
        return []
