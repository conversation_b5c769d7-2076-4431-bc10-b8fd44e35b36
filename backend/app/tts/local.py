import asyncio
import base64
import logging
import subprocess
from typing import Async<PERSON>enerator

from .base import TTSBase

logger = logging.getLogger(__name__)


class LocalTTS(TTSBase):
    def __init__(self):
        # Check if espeak-ng is installed
        try:
            subprocess.run(["espeak", "--version"], check=True, capture_output=True)
            logger.info("espeak found.")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("espeak is not installed. Please install it to use LocalTTS.")
            raise

    async def stream(
        self, text_generator: AsyncGenerator[str, None]
    ) -> AsyncGenerator[str, None]:
        text = "".join([chunk async for chunk in text_generator])
        if not text:
            return

        try:
            # Use espeak to generate WAV audio data to stdout
            process = await asyncio.create_subprocess_exec(
                "espeak",
                "-a",
                "200",  # Amplitude
                "-s",
                "150",  # Speed
                "--stdout",
                text,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )

            while True:
                audio_chunk = await process.stdout.read(4096)
                if not audio_chunk:
                    break
                yield base64.b64encode(audio_chunk).decode("utf-8")

            await process.wait()
            if process.returncode != 0:
                stderr = await process.stderr.read()
                logger.error(f"espeak error: {stderr.decode()}")

        except Exception as e:
            logger.error(f"Error in Local TTS stream: {e}")
