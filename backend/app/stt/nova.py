import asyncio
import logging

from deepgram import DeepgramClient, LiveOptions, LiveTranscriptionEvents

from ..config import settings
from .base import STTBase

logger = logging.getLogger(__name__)


class NovaSTT(STTBase):
    def __init__(self):
        self.client = DeepgramClient(settings.nova_api_key)

    async def transcribe(
        self,
        audio_queue: asyncio.Queue,
        transcript_queue: asyncio.Queue,
        sample_rate: int = 16000,
    ):
        logger.info("Starting Nova (Deepgram) transcription...")

        try:
            dg_connection = self.client.listen.asynclive.v("1")

            async def on_message(self, result, **kwargs):
                transcript = result.channel.alternatives[0].transcript
                if transcript:
                    is_final = result.is_final
                    await transcript_queue.put((transcript, is_final))

            async def on_error(self, error, **kwargs):
                logger.error(f"Deepgram error: {error}")

            dg_connection.on(LiveTranscriptionEvents.Transcript, on_message)
            dg_connection.on(LiveTranscriptionEvents.Error, on_error)

            options = LiveOptions(
                model="nova-2",
                language="en-US",
                smart_format=True,
                encoding="linear16",
                sample_rate=sample_rate,
            )

            await dg_connection.start(options)

            while True:
                audio_chunk = await audio_queue.get()
                if audio_chunk is None:
                    break
                await dg_connection.send(audio_chunk)

        except Exception as e:
            logger.error(f"Error in Nova (Deepgram) transcription: {e}")
        finally:
            if "dg_connection" in locals() and await dg_connection.is_connected():
                await dg_connection.finish()
            await transcript_queue.put((None, None))
            logger.info("Nova (Deepgram) transcription finished.")
