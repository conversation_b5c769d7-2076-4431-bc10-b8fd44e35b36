import asyncio
import base64
import json
import logging

import assemblyai as aai

from ..config import settings
from .base import STTBase

logger = logging.getLogger(__name__)


class AssemblyAI(STTBase):
    def __init__(self):
        aai.settings.api_key = settings.assemblyai_api_key

    async def transcribe(
        self,
        audio_queue: asyncio.Queue,
        transcript_queue: asyncio.Queue,
        sample_rate: int = 16000,
    ):
        async def on_data(data: aai.RealtimeTranscript):
            is_final = isinstance(data, aai.RealtimeFinalTranscript)
            if data.text:
                if is_final:
                    logger.info(f"User: {data.text}")
                    await transcript_queue.put((data.text, True))
                else:
                    logger.info(f"User (interim): {data.text}")
                    await transcript_queue.put((data.text, False))

        async def on_error(error: aai.RealtimeError):
            logger.error(f"AssemblyAI error: {error}")

        transcriber = aai.RealtimeTranscriber(
            on_data=on_data,
            on_error=on_error,
            sample_rate=sample_rate,
        )

        await transcriber.connect()

        try:
            while True:
                audio_chunk = await audio_queue.get()
                if audio_chunk is None:
                    break

                # The new SDK handles encoding, so we send raw bytes
                await transcriber.stream(audio_chunk)
        except Exception as e:
            logger.error(f"Error in AssemblyAI transcription: {e}")
        finally:
            await transcriber.close()
            await transcript_queue.put((None, None))  # Signal end of transcription
