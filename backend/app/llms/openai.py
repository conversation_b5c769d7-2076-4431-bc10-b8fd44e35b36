import os
from typing import AsyncGenerator, Dict, List

from openai import Async<PERSON><PERSON>A<PERSON>

from ..config import settings
from .base import ConversationalLLM


class OpenAI_LLM(ConversationalLLM):
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.openai_api_key)
        self.model = settings.openai_model

    async def chat(
        self, messages: List[Dict[str, str]], stream: bool = True
    ) -> AsyncGenerator[str, None]:
        if not stream:
            response = await self.client.chat.completions.create(
                model=self.model, messages=messages, stream=False
            )
            yield response.choices[0].message.content
            return

        stream_response = await self.client.chat.completions.create(
            model=self.model, messages=messages, stream=True
        )
        async for chunk in stream_response:
            if chunk.choices[0].delta.content is not None:
                yield chunk.choices[0].delta.content
