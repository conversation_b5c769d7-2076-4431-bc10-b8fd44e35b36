import asyncio
import base64
import json
import logging

import assemblyai as aai
from fastapi import WebSocket, WebSocketDisconnect

from ..config import settings

logger = logging.getLogger(__name__)


class AssemblyAIService:
    def __init__(self):
        aai.settings.api_key = settings.assemblyai_api_key

    async def transcribe_websocket(
        self, audio_queue: asyncio.Queue, transcript_queue: asyncio.Queue
    ):
        async def on_data(data: aai.RealtimeTranscript):
            if not data.text:
                return

            if isinstance(data, aai.RealtimeFinalTranscript):
                logger.info(f"User: {data.text}")
                await transcript_queue.put(data.text)
            else:
                logger.info(f"User (interim): {data.text}")

        async def on_error(error: aai.RealtimeError):
            logger.error(f"AssemblyAI error: {error}")

        transcriber = aai.RealtimeTranscriber(
            on_data=on_data,
            on_error=on_error,
            sample_rate=44_100,
        )

        await transcriber.connect()

        try:
            while True:
                audio_chunk = await audio_queue.get()
                if audio_chunk is None:
                    break

                # The audio from the websocket is raw bytes, but we need to handle the format.
                # Assuming it's mulaw, we need to decode it first.
                # However, the web client sends raw PCM data, so we can just encode it to base64

                data = base64.b64encode(audio_chunk).decode("utf-8")
                await transcriber.stream(json.dumps({"audio_data": data}))
        except Exception as e:
            logger.error(f"Error in AssemblyAI transcription: {e}")
        finally:
            await transcriber.close()


assemblyai_service = AssemblyAIService()
