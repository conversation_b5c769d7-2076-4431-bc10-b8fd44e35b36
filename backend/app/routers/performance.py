from fastapi import APIRouter
from typing import List, Dict, Any
from app.performance_tracker import get_metrics, clear_metrics

router = APIRouter()

@router.get("/metrics", response_model=List[Dict[str, Any]])
async def read_metrics():
    """
    Retrieve all collected performance metrics.
    """
    return get_metrics()

@router.delete("/metrics")
async def reset_metrics():
    """
    Clear all collected performance metrics.
    """
    clear_metrics()
    return {"message": "Metrics cleared successfully."}
