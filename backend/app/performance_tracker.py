import time
from functools import wraps
from typing import List, Dict, Any
import threading

# Thread-local storage for metrics
local_storage = threading.local()
local_storage.metrics = []

def track_latency(service: str, action: str):
    """
    A decorator to measure and record the latency of function calls.
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            latency = end_time - start_time
            
            if not hasattr(local_storage, 'metrics'):
                local_storage.metrics = []
            
            local_storage.metrics.append({
                "service": service,
                "action": action,
                "latency": latency,
                "timestamp": end_time
            })
            return result
        return wrapper
    return decorator

def get_metrics() -> List[Dict[str, Any]]:
    """
    Retrieves the collected performance metrics.
    """
    if hasattr(local_storage, 'metrics'):
        return local_storage.metrics
    return []

def clear_metrics():
    """
    Clears the collected performance metrics.
    """
    if hasattr(local_storage, 'metrics'):
        local_storage.metrics = []
