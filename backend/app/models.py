from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>an,
    Column,
    DateTime,
    Float,
    Foreign<PERSON>ey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .database import Base


class Customer(Base):
    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    phone = Column(String, unique=True, nullable=False, index=True)
    email = Column(String, index=True)
    address = Column(Text)
    city = Column(String)
    state = Column(String)
    zip_code = Column(String)
    country = Column(String, default="US")
    notes = Column(Text)
    enrichment = Column(JSON)  # Additional info for voice calls
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    carts = relationship("Cart", back_populates="customer")
    call_history = relationship("CallHistory", back_populates="customer")


class Product(Base):
    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    description = Column(Text)
    price = Column(Float, nullable=False)
    category = Column(String, index=True)
    brand = Column(String)
    sku = Column(String, unique=True, index=True)
    sizes = Column(JSON)  # Available sizes as JSON array
    colors = Column(JSON)  # Available colors as JSON array
    image_url = Column(String)
    additional_images = Column(JSON)  # Additional product images
    stock_quantity = Column(Integer, default=0)
    weight = Column(Float)  # Product weight
    dimensions = Column(JSON)  # Length, width, height
    tags = Column(JSON)  # Product tags for search
    enrichment = Column(JSON)  # Additional info for voice calls
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    cart_items = relationship("CartItem", back_populates="product")


class Cart(Base):
    __tablename__ = "carts"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    status = Column(String, default="active")  # active, abandoned, completed
    total_amount = Column(Float, default=0.0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    customer = relationship("Customer", back_populates="carts")
    items = relationship(
        "CartItem", back_populates="cart", cascade="all, delete-orphan"
    )


class CartItem(Base):
    __tablename__ = "cart_items"

    id = Column(Integer, primary_key=True, index=True)
    cart_id = Column(Integer, ForeignKey("carts.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    quantity = Column(Integer, nullable=False, default=1)
    size = Column(String)
    color = Column(String)
    price = Column(Float, nullable=False)  # Price at time of adding to cart
    added_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    cart = relationship("Cart", back_populates="items")
    product = relationship("Product", back_populates="cart_items")


class CallHistory(Base):
    __tablename__ = "call_history"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    call_sid = Column(String, index=True)  # Twilio Call SID
    phone_number = Column(String, nullable=True)
    status = Column(String)  # initiated, in-progress, completed, failed
    call_type = Column(String, default="outbound")  # outbound, inbound
    call_duration = Column(Integer)  # Duration in seconds
    call_metadata = Column(JSON)  # Additional call metadata
    system_prompt = Column(Text)
    first_message = Column(Text)
    conversation_log = Column(JSON)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))

    # Relationships
    customer = relationship("Customer", back_populates="call_history")


class Order(Base):
    __tablename__ = "orders"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    total_amount = Column(Float, nullable=False)
    status = Column(
        String, default="pending"
    )  # pending, completed, shipped, delivered, cancelled
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    customer = relationship("Customer")
    items = relationship(
        "OrderItem", back_populates="order", cascade="all, delete-orphan"
    )


class OrderItem(Base):
    __tablename__ = "order_items"

    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    quantity = Column(Integer, nullable=False)
    price = Column(Float, nullable=False)
    size = Column(String)
    color = Column(String)

    # Relationships
    order = relationship("Order", back_populates="items")
    product = relationship("Product")
