import logging
import uuid

from fastapi import Depends, FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session

from . import models
from .config import settings
from .database import engine, get_db
from .logging_config import setup_logging
from .routers import (
    customers,
    frontend_ws,
    phone_stream,
    products,
    text_chat,
    twilio,
    voice_calls,
    web_call,
    webhooks,
)

# --- Logging Configuration ---
setup_logging()

# Forcefully set the log level for noisy libraries to WARNING
logging.getLogger("websockets").setLevel(logging.WARNING)
logging.getLogger("httpx").setLevel(logging.WARNING)

logger = logging.getLogger(__name__)

# Create database tables
models.Base.metadata.create_all(bind=engine)

# Create FastAPI app
app = FastAPI(
    title="E-commerce & Call Center API",
    description="Modern e-commerce platform with integrated voice calling and AI agent capabilities",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)


# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(customers.router, prefix="/api")
app.include_router(products.router, prefix="/api")
app.include_router(voice_calls.router, prefix="/api")
app.include_router(webhooks.router, prefix="/api")
app.include_router(web_call.router, prefix="/api")
app.include_router(phone_stream.router, prefix="/api")
app.include_router(text_chat.router, prefix="/api")
app.include_router(twilio.router, prefix="/api")
app.include_router(frontend_ws.router)


# Health check endpoint
@app.get("/")
def read_root():
    return {
        "message": "E-commerce & Call Center API",
        "version": "1.0.0",
        "status": "healthy",
    }


@app.get("/health")
def health_check():
    return {"status": "healthy", "message": "API is running"}


# Database status endpoint
@app.get("/api/status")
def api_status(db: Session = Depends(get_db)):
    try:
        # Test database connection
        customer_count = db.query(models.Customer).count()
        product_count = db.query(models.Product).count()

        return {
            "status": "healthy",
            "database": "connected",
            "customers": customer_count,
            "products": product_count,
            "twilio_configured": bool(settings.twilio_account_sid),
            "elevenlabs_configured": bool(settings.elevenlabs_api_key),
        }
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}
